camera_enable_para:
  monitor_node: 0
  rtmp_node: 0
  convert_node: 0
  encode_node: 0
  decode_node: 1
  rtsp_push_node: 0
  rtsp_pull_node: 1
  mp4_save_node: 0
  image_radar_align_node: 0
camera_common_para:
  decode_rate: 3                                                         # 解码帧率/HZ
 
cam_rtsp_para:
  stream_num: 4                                                          # 决定拉流与解码路数
  cameras:
    # camera 0
    - camera_type: 0                                                     # 相机类型：天翼:0, 海康:1, 大华:2, 宇视:3（若为其他则保持0）
      pull_url: "rtsp://admin:@Tyjt101@192.168.36.232:554/Streaming/Channels/103"
      # pull_url: "rtsp://172.26.9.62:8554/stream1"
      # pull_url: "rtsp://192.168.3.103:8554/live_1080p/R27_Aw_CamS"

    # camera 1
    - camera_type: 0
      pull_url: "rtsp://admin:@Tyjt101@192.168.36.41:554/Streaming/Channels/103"
      # pull_url: "rtsp://172.26.9.62:8554/stream2"
      # pull_url: "rtsp://192.168.3.103:8554/live_1080p/R27_Bn_CamW"

    # camera 2
    - camera_type: 0
      pull_url: "rtsp://admin:@Tyjt101@192.168.36.232:554/Streaming/Channels/103"
      # pull_url: "rtsp://172.26.9.62:8554/stream3"
      # pull_url: "rtsp://192.168.3.103:8554/live_1080p/R27_Ce_CamN"

    # camera 3
    - camera_type: 0
      pull_url: "rtsp://admin:@Tyjt101@192.168.36.41:554/Streaming/Channels/103"
      # pull_url: "rtsp://172.26.9.62:8554/stream4"
      # pull_url: "rtsp://192.168.3.103:8554/live_1080p/R27_Aw_CamS"